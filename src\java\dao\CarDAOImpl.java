package dao;

import context.DBContext;
import models.Car;
import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class CarDAOImpl implements CarDAO {
    
    @Override
    public List<Car> getAllCars() throws SQLException {
        List<Car> cars = new ArrayList<>();
        String sql = "SELECT CarID, CarName, Manufacturer, Price, ReleasedYear FROM Cars ORDER BY CarID";
        
        try (DBContext dbContext = new DBContext();
             Connection conn = dbContext.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()) {
            
            while (rs.next()) {
                Car car = new Car();
                car.setCarID(rs.getInt("CarID"));
                car.setCarName(rs.getString("CarName"));
                car.setManufacturer(rs.getString("Manufacturer"));
                car.setPrice(rs.getBigDecimal("Price"));
                car.setReleasedYear(rs.getInt("ReleasedYear"));
                cars.add(car);
            }
        }
        return cars;
    }
    
    @Override
    public Car getCarById(int carID) throws SQLException {
        String sql = "SELECT CarID, CarName, Manufacturer, Price, ReleasedYear FROM Cars WHERE CarID = ?";
        
        try (DBContext dbContext = new DBContext();
             Connection conn = dbContext.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            
            ps.setInt(1, carID);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    Car car = new Car();
                    car.setCarID(rs.getInt("CarID"));
                    car.setCarName(rs.getString("CarName"));
                    car.setManufacturer(rs.getString("Manufacturer"));
                    car.setPrice(rs.getBigDecimal("Price"));
                    car.setReleasedYear(rs.getInt("ReleasedYear"));
                    return car;
                }
            }
        }
        return null;
    }
    
    @Override
    public boolean addCar(Car car) throws SQLException {
        String sql = "INSERT INTO Cars (CarID, CarName, Manufacturer, Price, ReleasedYear) VALUES (?, ?, ?, ?, ?)";
        
        try (DBContext dbContext = new DBContext();
             Connection conn = dbContext.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            
            ps.setInt(1, car.getCarID());
            ps.setString(2, car.getCarName());
            ps.setString(3, car.getManufacturer());
            ps.setBigDecimal(4, car.getPrice());
            ps.setInt(5, car.getReleasedYear());
            
            int rowsAffected = ps.executeUpdate();
            return rowsAffected > 0;
        }
    }
    
    @Override
    public boolean updateCar(Car car) throws SQLException {
        String sql = "UPDATE Cars SET CarName = ?, Manufacturer = ?, Price = ?, ReleasedYear = ? WHERE CarID = ?";
        
        try (DBContext dbContext = new DBContext();
             Connection conn = dbContext.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            
            ps.setString(1, car.getCarName());
            ps.setString(2, car.getManufacturer());
            ps.setBigDecimal(3, car.getPrice());
            ps.setInt(4, car.getReleasedYear());
            ps.setInt(5, car.getCarID());
            
            int rowsAffected = ps.executeUpdate();
            return rowsAffected > 0;
        }
    }
    
    @Override
    public boolean deleteCar(int carID) throws SQLException {
        String sql = "DELETE FROM Cars WHERE CarID = ?";
        
        try (DBContext dbContext = new DBContext();
             Connection conn = dbContext.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            
            ps.setInt(1, carID);
            int rowsAffected = ps.executeUpdate();
            return rowsAffected > 0;
        }
    }
    
    @Override
    public boolean carExists(int carID) throws SQLException {
        String sql = "SELECT COUNT(*) FROM Cars WHERE CarID = ?";
        
        try (DBContext dbContext = new DBContext();
             Connection conn = dbContext.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            
            ps.setInt(1, carID);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        return false;
    }
}
