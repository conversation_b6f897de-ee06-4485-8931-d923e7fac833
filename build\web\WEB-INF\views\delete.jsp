<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Car - Automobile Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #dc3545;
            margin-bottom: 20px;
        }
        .warning-message {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        h2 {
            color: #666;
            margin-bottom: 20px;
            font-size: 18px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-label {
            font-weight: bold;
            color: #333;
            width: 150px;
            flex-shrink: 0;
        }
        .detail-value {
            color: #666;
            flex: 1;
        }
        .actions {
            margin-top: 30px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 15px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .error-message {
            color: #dc3545;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
        }
        .price {
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Delete</h1>
        
        <div class="warning-message">
            Are you sure you want to delete this?
        </div>
        
        <c:if test="${not empty errorMessage}">
            <div class="error-message">
                ${errorMessage}
            </div>
        </c:if>
        
        <h2>Car</h2>
        
        <div class="detail-row">
            <div class="detail-label">CarId</div>
            <div class="detail-value">${car.carID}</div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">CarName</div>
            <div class="detail-value">${car.carName}</div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Manufacturer</div>
            <div class="detail-value">${car.manufacturer}</div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Price</div>
            <div class="detail-value price">
                <fmt:formatNumber value="${car.price}" type="currency" currencySymbol="$" maxFractionDigits="2"/>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">ReleasedYear</div>
            <div class="detail-value">${car.releasedYear}</div>
        </div>
        
        <div class="actions">
            <form action="${pageContext.request.contextPath}/cars" method="post" style="display: inline;">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="carID" value="${car.carID}">
                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this car?')">Delete</button>
            </form>
            <a href="${pageContext.request.contextPath}/cars" class="btn btn-secondary">Back to List</a>
        </div>
    </div>
</body>
</html>
