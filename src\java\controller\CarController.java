package controller;

import dao.CarDAO;
import dao.CarDAOImpl;
import models.Car;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;

@WebServlet(name = "CarController", urlPatterns = {"/cars"})
public class CarController extends HttpServlet {
    
    private CarDAO carDAO;
    
    @Override
    public void init() throws ServletException {
        carDAO = new CarDAOImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String action = request.getParameter("action");
        if (action == null) {
            action = "list";
        }
        
        try {
            switch (action) {
                case "list":
                    listCars(request, response);
                    break;
                case "create":
                    showCreateForm(request, response);
                    break;
                case "edit":
                    showEditForm(request, response);
                    break;
                case "delete":
                    showDeleteForm(request, response);
                    break;
                case "details":
                    showDetails(request, response);
                    break;
                default:
                    listCars(request, response);
                    break;
            }
        } catch (SQLException e) {
            throw new ServletException("Database error", e);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        String action = request.getParameter("action");
        
        try {
            switch (action) {
                case "create":
                    createCar(request, response);
                    break;
                case "update":
                    updateCar(request, response);
                    break;
                case "delete":
                    deleteCar(request, response);
                    break;
                default:
                    listCars(request, response);
                    break;
            }
        } catch (SQLException e) {
            throw new ServletException("Database error", e);
        }
    }
    
    private void listCars(HttpServletRequest request, HttpServletResponse response)
            throws SQLException, ServletException, IOException {
        List<Car> cars = carDAO.getAllCars();
        request.setAttribute("cars", cars);
        request.getRequestDispatcher("/WEB-INF/views/list_simple.jsp").forward(request, response);
    }
    
    private void showCreateForm(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.getRequestDispatcher("/WEB-INF/views/create.jsp").forward(request, response);
    }
    
    private void showEditForm(HttpServletRequest request, HttpServletResponse response)
            throws SQLException, ServletException, IOException {
        int carID = Integer.parseInt(request.getParameter("id"));
        Car car = carDAO.getCarById(carID);
        request.setAttribute("car", car);
        request.getRequestDispatcher("/WEB-INF/views/edit.jsp").forward(request, response);
    }
    
    private void showDeleteForm(HttpServletRequest request, HttpServletResponse response)
            throws SQLException, ServletException, IOException {
        int carID = Integer.parseInt(request.getParameter("id"));
        Car car = carDAO.getCarById(carID);
        request.setAttribute("car", car);
        request.getRequestDispatcher("/WEB-INF/views/delete.jsp").forward(request, response);
    }
    
    private void showDetails(HttpServletRequest request, HttpServletResponse response)
            throws SQLException, ServletException, IOException {
        int carID = Integer.parseInt(request.getParameter("id"));
        Car car = carDAO.getCarById(carID);
        request.setAttribute("car", car);
        request.getRequestDispatcher("/WEB-INF/views/details.jsp").forward(request, response);
    }

    private void createCar(HttpServletRequest request, HttpServletResponse response)
            throws SQLException, ServletException, IOException {

        // Get parameters from form
        String carIDStr = request.getParameter("carID");
        String carName = request.getParameter("carName");
        String manufacturer = request.getParameter("manufacturer");
        String priceStr = request.getParameter("price");
        String releasedYearStr = request.getParameter("releasedYear");

        // Validate input
        String errorMessage = validateCarInput(carIDStr, carName, manufacturer, priceStr, releasedYearStr);
        if (errorMessage != null) {
            request.setAttribute("errorMessage", errorMessage);
            request.getRequestDispatcher("/WEB-INF/views/create.jsp").forward(request, response);
            return;
        }

        try {
            int carID = Integer.parseInt(carIDStr);
            BigDecimal price = new BigDecimal(priceStr);
            int releasedYear = Integer.parseInt(releasedYearStr);

            // Check if car ID already exists
            if (carDAO.carExists(carID)) {
                request.setAttribute("errorMessage", "Car ID already exists. Please choose a different ID.");
                request.getRequestDispatcher("/WEB-INF/views/create.jsp").forward(request, response);
                return;
            }

            Car car = new Car(carID, carName, manufacturer, price, releasedYear);
            boolean success = carDAO.addCar(car);

            if (success) {
                response.sendRedirect(request.getContextPath() + "/cars?action=list");
            } else {
                request.setAttribute("errorMessage", "Failed to create car. Please try again.");
                request.getRequestDispatcher("/WEB-INF/views/create.jsp").forward(request, response);
            }
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "Invalid number format in Car ID, Price, or Released Year.");
            request.getRequestDispatcher("/WEB-INF/views/create.jsp").forward(request, response);
        }
    }

    private void updateCar(HttpServletRequest request, HttpServletResponse response)
            throws SQLException, ServletException, IOException {

        // Get parameters from form
        String carIDStr = request.getParameter("carID");
        String carName = request.getParameter("carName");
        String manufacturer = request.getParameter("manufacturer");
        String priceStr = request.getParameter("price");
        String releasedYearStr = request.getParameter("releasedYear");

        // Validate input
        String errorMessage = validateCarInput(carIDStr, carName, manufacturer, priceStr, releasedYearStr);
        if (errorMessage != null) {
            int carID = Integer.parseInt(carIDStr);
            Car car = carDAO.getCarById(carID);
            request.setAttribute("car", car);
            request.setAttribute("errorMessage", errorMessage);
            request.getRequestDispatcher("/WEB-INF/views/edit.jsp").forward(request, response);
            return;
        }

        try {
            int carID = Integer.parseInt(carIDStr);
            BigDecimal price = new BigDecimal(priceStr);
            int releasedYear = Integer.parseInt(releasedYearStr);

            Car car = new Car(carID, carName, manufacturer, price, releasedYear);
            boolean success = carDAO.updateCar(car);

            if (success) {
                response.sendRedirect(request.getContextPath() + "/cars?action=list");
            } else {
                request.setAttribute("car", car);
                request.setAttribute("errorMessage", "Failed to update car. Please try again.");
                request.getRequestDispatcher("/WEB-INF/views/edit.jsp").forward(request, response);
            }
        } catch (NumberFormatException e) {
            int carID = Integer.parseInt(carIDStr);
            Car car = carDAO.getCarById(carID);
            request.setAttribute("car", car);
            request.setAttribute("errorMessage", "Invalid number format in Price or Released Year.");
            request.getRequestDispatcher("/WEB-INF/views/edit.jsp").forward(request, response);
        }
    }

    private void deleteCar(HttpServletRequest request, HttpServletResponse response)
            throws SQLException, ServletException, IOException {

        int carID = Integer.parseInt(request.getParameter("carID"));
        boolean success = carDAO.deleteCar(carID);

        if (success) {
            response.sendRedirect(request.getContextPath() + "/cars?action=list");
        } else {
            Car car = carDAO.getCarById(carID);
            request.setAttribute("car", car);
            request.setAttribute("errorMessage", "Failed to delete car. Please try again.");
            request.getRequestDispatcher("/WEB-INF/views/delete.jsp").forward(request, response);
        }
    }

    private String validateCarInput(String carIDStr, String carName, String manufacturer,
                                  String priceStr, String releasedYearStr) {

        if (carIDStr == null || carIDStr.trim().isEmpty()) {
            return "Car ID is required.";
        }
        if (carName == null || carName.trim().isEmpty()) {
            return "Car Name is required.";
        }
        if (manufacturer == null || manufacturer.trim().isEmpty()) {
            return "Manufacturer is required.";
        }
        if (priceStr == null || priceStr.trim().isEmpty()) {
            return "Price is required.";
        }
        if (releasedYearStr == null || releasedYearStr.trim().isEmpty()) {
            return "Released Year is required.";
        }

        try {
            int carID = Integer.parseInt(carIDStr);
            if (carID <= 0) {
                return "Car ID must be a positive number.";
            }
        } catch (NumberFormatException e) {
            return "Car ID must be a valid number.";
        }

        try {
            BigDecimal price = new BigDecimal(priceStr);
            if (price.compareTo(BigDecimal.ZERO) <= 0) {
                return "Price must be greater than 0.";
            }
        } catch (NumberFormatException e) {
            return "Price must be a valid number.";
        }

        try {
            int releasedYear = Integer.parseInt(releasedYearStr);
            if (releasedYear < 1900 || releasedYear > 2030) {
                return "Released Year must be between 1900 and 2030.";
            }
        } catch (NumberFormatException e) {
            return "Released Year must be a valid number.";
        }

        return null; // No validation errors
    }
}
