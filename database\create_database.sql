-- Automobile Management System Database Setup
-- Run this script in SQL Server Management Studio

-- Create database
CREATE DATABASE MyStock;
GO

-- Use the database
USE MyStock;
GO

-- Create Cars table
CREATE TABLE Cars (
    CarID INT NOT NULL PRIMARY KEY,
    CarName VARCHAR(50) NOT NULL,
    Manufacturer VARCHAR(50) NOT NULL,
    Price MONEY NOT NULL,
    ReleasedYear INT NOT NULL
);
GO

-- Insert sample data
INSERT INTO Cars (CarID, CarName, Manufacturer, Price, ReleasedYear) VALUES
(1, 'Accord', 'Honda Motor Company', 24970.0000, 2021),
(3, 'Clarity', 'Honda Motor Company', 33400.0000, 2021),
(4, 'BMW 8 Series', 'BMW', 85000.0000, 2021),
(5, 'Audi A6', 'Audi', 14000.0000, 2020);
GO

-- Verify data
SELECT * FROM Cars ORDER BY CarID;
GO

-- Create additional indexes for better performance (optional)
CREATE INDEX IX_Cars_Manufacturer ON Cars(Manufacturer);
CREATE INDEX IX_Cars_ReleasedYear ON Cars(ReleasedYear);
GO

PRINT 'Database setup completed successfully!';
