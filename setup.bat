@echo off
echo Setting up Automobile Management System...

REM Create lib directory if it doesn't exist
if not exist "web\WEB-INF\lib" (
    mkdir "web\WEB-INF\lib"
    echo Created web\WEB-INF\lib directory
)

echo.
echo Setup completed!
echo.
echo Next steps:
echo 1. Download SQL Server JDBC Driver from:
echo    https://docs.microsoft.com/en-us/sql/connect/jdbc/download-microsoft-jdbc-driver-for-sql-server
echo    Copy mssql-jdbc-x.x.x.jre17.jar to web\WEB-INF\lib\
echo.
echo 2. Download JSTL 1.2 from:
echo    https://mvnrepository.com/artifact/javax.servlet/jstl/1.2
echo    Copy jstl-1.2.jar to web\WEB-INF\lib\
echo.
echo 3. Run database\create_database.sql in SQL Server Management Studio
echo.
echo 4. Update database connection in src\java\context\DBContext.java
echo.
echo 5. Build and run the project in NetBeans
echo.
pause
