<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automobile Management</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #f8f9fa;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .navbar {
            color: #6c757d;
            font-size: 18px;
            font-weight: bold;
        }
        .navbar a {
            color: #6c757d;
            text-decoration: none;
            margin-right: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .create-link {
            color: #007bff;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        .create-link:hover {
            text-decoration: underline;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .action-links a {
            color: #007bff;
            text-decoration: none;
            margin-right: 10px;
        }
        .action-links a:hover {
            text-decoration: underline;
        }
        .price {
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="navbar">
                AutomobileWebApp &nbsp;&nbsp;
                <a href="${pageContext.request.contextPath}/cars">Home</a>
                <a href="#">Privacy</a>
            </div>
        </div>
    </div>

    <div class="container">
        <h1>Car List</h1>
        
        <a href="${pageContext.request.contextPath}/cars?action=create" class="create-link">Create New</a>
        
        <table>
            <thead>
                <tr>
                    <th>CarId</th>
                    <th>CarName</th>
                    <th>Manufacturer</th>
                    <th>Price</th>
                    <th>ReleasedYear</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <c:forEach var="car" items="${cars}">
                    <tr>
                        <td>${car.carID}</td>
                        <td>${car.carName}</td>
                        <td>${car.manufacturer}</td>
                        <td class="price"><fmt:formatNumber value="${car.price}" type="currency" currencySymbol="$" maxFractionDigits="2"/></td>
                        <td>${car.releasedYear}</td>
                        <td class="action-links">
                            <a href="${pageContext.request.contextPath}/cars?action=edit&id=${car.carID}">Edit</a> |
                            <a href="${pageContext.request.contextPath}/cars?action=details&id=${car.carID}">Details</a> |
                            <a href="${pageContext.request.contextPath}/cars?action=delete&id=${car.carID}">Delete</a>
                        </td>
                    </tr>
                </c:forEach>
                <c:if test="${empty cars}">
                    <tr>
                        <td colspan="6" style="text-align: center; color: #666;">No cars found.</td>
                    </tr>
                </c:if>
            </tbody>
        </table>
    </div>
</body>
</html>
