<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Car Details - Automobile Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }
        h2 {
            color: #666;
            margin-bottom: 20px;
            font-size: 18px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-label {
            font-weight: bold;
            color: #333;
            width: 150px;
            flex-shrink: 0;
        }
        .detail-value {
            color: #666;
            flex: 1;
        }
        .actions {
            margin-top: 30px;
            text-align: left;
        }
        .action-link {
            color: #007bff;
            text-decoration: none;
            margin-right: 15px;
        }
        .action-link:hover {
            text-decoration: underline;
        }
        .price {
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Car Details</h1>
        
        <h2>Car</h2>
        
        <div class="detail-row">
            <div class="detail-label">CarId</div>
            <div class="detail-value">${car.carID}</div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">CarName</div>
            <div class="detail-value">${car.carName}</div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Manufacturer</div>
            <div class="detail-value">${car.manufacturer}</div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Price</div>
            <div class="detail-value price">
                <fmt:formatNumber value="${car.price}" type="currency" currencySymbol="$" maxFractionDigits="2"/>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">ReleasedYear</div>
            <div class="detail-value">${car.releasedYear}</div>
        </div>
        
        <div class="actions">
            <a href="${pageContext.request.contextPath}/cars?action=edit&id=${car.carID}" class="action-link">Edit</a>
            <a href="${pageContext.request.contextPath}/cars" class="action-link">Back to List</a>
        </div>
    </div>
</body>
</html>
