annotation.processing.enabled=true
annotation.processing.enabled.in.editor=true
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
build.classes.dir=${build.web.dir}/WEB-INF/classes
build.classes.excludes=**/*.java,**/*.form
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
build.web.dir=${build.dir}/web
build.web.excludes=${build.classes.excludes}
client.urlPart=
compile.jsps=false
conf.dir=${source.root}/conf
debug.classpath=${build.classes.dir}:${javac.classpath}
debug.test.classpath=\
    ${run.test.classpath}
display.browser=true
# Files to be excluded from distribution war
dist.archive.excludes=
dist.dir=dist
dist.ear.war=${dist.dir}/${war.ear.name}
dist.javadoc.dir=${dist.dir}/javadoc
dist.war=${dist.dir}/${war.name}
excludes=
file.reference.jakarta.servlet.jsp.jstl-2.0.0.jar=D:\\cy\\swp\\quanlycuahang\\jakarta.servlet.jsp.jstl-2.0.0.jar
file.reference.jakarta.servlet.jsp.jstl-api-2.0.0.jar=D:\\cy\\swp\\quanlycuahang\\jakarta.servlet.jsp.jstl-api-2.0.0.jar
file.reference.jaxb-api-2.1.jar=D:\\cy\\swp\\quanlycuahang\\jaxb-api-2.1.jar
file.reference.sqljdbc42.jar=D:\\cy\\swp\\quanlycuahang\\sqljdbc42.jar
includes=**
j2ee.compile.on.save=true
j2ee.copy.static.files.on.save=true
j2ee.deploy.on.save=true
j2ee.platform=8.0-web
j2ee.platform.classpath=${j2ee.server.home}/bin/tomcat-juli.jar:${j2ee.server.home}/lib/annotations-api.jar:${j2ee.server.home}/lib/catalina-ant.jar:${j2ee.server.home}/lib/catalina-ha.jar:${j2ee.server.home}/lib/catalina-ssi.jar:${j2ee.server.home}/lib/catalina-storeconfig.jar:${j2ee.server.home}/lib/catalina-tribes.jar:${j2ee.server.home}/lib/catalina.jar:${j2ee.server.home}/lib/ecj-4.20.jar:${j2ee.server.home}/lib/el-api.jar:${j2ee.server.home}/lib/jasper-el.jar:${j2ee.server.home}/lib/jasper.jar:${j2ee.server.home}/lib/jaspic-api.jar:${j2ee.server.home}/lib/jsp-api.jar:${j2ee.server.home}/lib/servlet-api.jar:${j2ee.server.home}/lib/tomcat-api.jar:${j2ee.server.home}/lib/tomcat-coyote-ffm.jar:${j2ee.server.home}/lib/tomcat-coyote.jar:${j2ee.server.home}/lib/tomcat-dbcp.jar:${j2ee.server.home}/lib/tomcat-i18n-cs.jar:${j2ee.server.home}/lib/tomcat-i18n-de.jar:${j2ee.server.home}/lib/tomcat-i18n-es.jar:${j2ee.server.home}/lib/tomcat-i18n-fr.jar:${j2ee.server.home}/lib/tomcat-i18n-ja.jar:${j2ee.server.home}/lib/tomcat-i18n-ko.jar:${j2ee.server.home}/lib/tomcat-i18n-pt-BR.jar:${j2ee.server.home}/lib/tomcat-i18n-ru.jar:${j2ee.server.home}/lib/tomcat-i18n-zh-CN.jar:${j2ee.server.home}/lib/tomcat-jdbc.jar:${j2ee.server.home}/lib/tomcat-jni.jar:${j2ee.server.home}/lib/tomcat-util-scan.jar:${j2ee.server.home}/lib/tomcat-util.jar:${j2ee.server.home}/lib/tomcat-websocket.jar:${j2ee.server.home}/lib/websocket-api.jar
j2ee.server.type=Tomcat
jar.compress=false
javac.classpath=\
    ${file.reference.jakarta.servlet.jsp.jstl-2.0.0.jar}:\
    ${file.reference.jakarta.servlet.jsp.jstl-api-2.0.0.jar}:\
    ${file.reference.jaxb-api-2.1.jar}:\
    ${file.reference.sqljdbc42.jar}
# Space-separated list of extra javac options
javac.compilerargs=
javac.debug=true
javac.deprecation=false
javac.processorpath=\
    ${javac.classpath}
javac.source=17
javac.target=17
javac.test.classpath=\
    ${javac.classpath}:\
    ${build.classes.dir}
javac.test.processorpath=\
    ${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.preview=true
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
lib.dir=${web.docbase.dir}/WEB-INF/lib
no.dependencies=false
persistence.xml.dir=${conf.dir}
platform.active=default_platform
resource.dir=setup
run.test.classpath=\
    ${javac.test.classpath}:\
    ${build.test.classes.dir}
# Space-separated list of JVM arguments used when running a class with a main method or a unit test
# (you may also define separate properties like run-sys-prop.name=value instead of -Dname=value):
runmain.jvmargs=
source.encoding=UTF-8
source.root=src
src.dir=${source.root}/java
test.src.dir=test
war.content.additional=
war.ear.name=${war.name}
war.name=Automobile.war
web.docbase.dir=web
webinf.dir=web/WEB-INF
