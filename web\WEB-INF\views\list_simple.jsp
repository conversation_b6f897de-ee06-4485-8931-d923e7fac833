<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="models.Car" %>
<%@ page import="java.text.NumberFormat" %>
<%@ page import="java.util.Locale" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automobile Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #f8f9fa;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .navbar {
            color: #6c757d;
            font-size: 18px;
            font-weight: bold;
        }
        .navbar a {
            color: #6c757d;
            text-decoration: none;
            margin-right: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .create-link {
            color: #007bff;
            text-decoration: none;
            margin-bottom: 20px;
            display: inline-block;
        }
        .create-link:hover {
            text-decoration: underline;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .action-links a {
            color: #007bff;
            text-decoration: none;
            margin-right: 10px;
        }
        .action-links a:hover {
            text-decoration: underline;
        }
        .price {
            text-align: right;
        }
        .no-data {
            text-align: center;
            color: #666;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="navbar">
                AutomobileWebApp &nbsp;&nbsp;
                <a href="<%=request.getContextPath()%>/cars">Home</a>
                <a href="#">Privacy</a>
            </div>
        </div>
    </div>

    <div class="container">
        <h1>Car List</h1>
        
        <a href="<%=request.getContextPath()%>/cars?action=create" class="create-link">Create New</a>
        
        <table>
            <thead>
                <tr>
                    <th>CarId</th>
                    <th>CarName</th>
                    <th>Manufacturer</th>
                    <th>Price</th>
                    <th>ReleasedYear</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <%
                    List<Car> cars = (List<Car>) request.getAttribute("cars");
                    if (cars != null && !cars.isEmpty()) {
                        NumberFormat currencyFormat = NumberFormat.getCurrencyInstance(Locale.US);
                        for (Car car : cars) {
                %>
                    <tr>
                        <td><%=car.getCarID()%></td>
                        <td><%=car.getCarName()%></td>
                        <td><%=car.getManufacturer()%></td>
                        <td class="price"><%=currencyFormat.format(car.getPrice())%></td>
                        <td><%=car.getReleasedYear()%></td>
                        <td class="action-links">
                            <a href="<%=request.getContextPath()%>/cars?action=edit&id=<%=car.getCarID()%>">Edit</a> |
                            <a href="<%=request.getContextPath()%>/cars?action=details&id=<%=car.getCarID()%>">Details</a> |
                            <a href="<%=request.getContextPath()%>/cars?action=delete&id=<%=car.getCarID()%>">Delete</a>
                        </td>
                    </tr>
                <%
                        }
                    } else {
                %>
                    <tr>
                        <td colspan="6" class="no-data">No cars found. Please add some cars first.</td>
                    </tr>
                <%
                    }
                %>
            </tbody>
        </table>
    </div>
</body>
</html>
