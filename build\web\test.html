<!DOCTYPE html>
<html>
<head>
    <title>Test Page - Automobile Management</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-links {
            margin: 20px 0;
        }
        .test-links a {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            text-align: center;
        }
        .test-links a:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Automobile Management System - Test</h1>
        
        <div class="test-links">
            <a href="test">Test Database Connection</a>
            <a href="cars">Go to Car Management</a>
            <a href="cars?action=list">Car List</a>
            <a href="cars?action=create">Create New Car</a>
        </div>
        
        <h3>Project Structure Check:</h3>
        <ul>
            <li>✓ CarController servlet</li>
            <li>✓ DBContext for database connection</li>
            <li>✓ Car model class</li>
            <li>✓ CarDAO and CarDAOImpl</li>
            <li>✓ JSP views (list, create, edit, delete, details)</li>
            <li>✓ web.xml configuration</li>
        </ul>
        
        <h3>Requirements:</h3>
        <ul>
            <li>SQL Server running on localhost:1433</li>
            <li>Database 'MyStock' created</li>
            <li>Table 'Cars' with sample data</li>
            <li>JSTL library in WEB-INF/lib</li>
            <li>SQL Server JDBC driver in WEB-INF/lib</li>
        </ul>
    </div>
</body>
</html>
