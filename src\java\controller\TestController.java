package controller;

import context.DBContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;

public class TestController extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Test Connection</title>");
        out.println("</head>");
        out.println("<body>");
        out.println("<h1>Automobile Management System - Test</h1>");
        
        // Test database connection
        try {
            DBContext dbContext = new DBContext();
            Connection conn = dbContext.getConnection();
            
            if (conn != null && !conn.isClosed()) {
                out.println("<p style='color: green;'>✓ Database connection successful!</p>");
                out.println("<p>Database URL: ****************************************************</p>");
            } else {
                out.println("<p style='color: red;'>✗ Database connection failed!</p>");
            }
            
            dbContext.close();
            
        } catch (SQLException e) {
            out.println("<p style='color: red;'>✗ Database error: " + e.getMessage() + "</p>");
            e.printStackTrace();
        }
        
        out.println("<p><a href='" + request.getContextPath() + "/cars'>Go to Car Management</a></p>");
        out.println("</body>");
        out.println("</html>");
    }
}
