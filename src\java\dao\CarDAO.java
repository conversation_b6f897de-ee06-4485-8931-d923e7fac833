package dao;

import models.Car;
import java.sql.SQLException;
import java.util.List;

public interface CarDAO {
    
    /**
     * Get all cars from database
     * @return List of all cars
     * @throws SQLException
     */
    List<Car> getAllCars() throws SQLException;
    
    /**
     * Get a car by ID
     * @param carID
     * @return Car object or null if not found
     * @throws SQLException
     */
    Car getCarById(int carID) throws SQLException;
    
    /**
     * Add a new car to database
     * @param car
     * @return true if successful, false otherwise
     * @throws SQLException
     */
    boolean addCar(Car car) throws SQLException;
    
    /**
     * Update an existing car
     * @param car
     * @return true if successful, false otherwise
     * @throws SQLException
     */
    boolean updateCar(Car car) throws SQLException;
    
    /**
     * Delete a car by ID
     * @param carID
     * @return true if successful, false otherwise
     * @throws SQLException
     */
    boolean deleteCar(int carID) throws SQLException;
    
    /**
     * Check if a car ID already exists
     * @param carID
     * @return true if exists, false otherwise
     * @throws SQLException
     */
    boolean carExists(int carID) throws SQLException;
}
