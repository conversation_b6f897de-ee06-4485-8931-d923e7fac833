# Hướng dẫn Deploy Automobile Management System

## <PERSON><PERSON><PERSON> bư<PERSON><PERSON> cần thực hiện để khắc phục lỗi 404:

### 1. <PERSON><PERSON><PERSON> tra thư viện
Đ<PERSON><PERSON> bảo các file sau có trong `web/WEB-INF/lib/`:
- `jstl-1.2.jar`
- `mssql-jdbc-12.8.0.jre17.jar` (hoặc phiên bản tương tự)

### 2. Tạo database
Chạy script SQL trong file `database/create_database.sql`:

```sql
CREATE DATABASE MyStock;
GO
USE MyStock;
GO

CREATE TABLE Cars (
    CarID INT NOT NULL PRIMARY KEY,
    CarName VARCHAR(50) NOT NULL,
    Manufacturer VARCHAR(50) NOT NULL,
    Price MONEY NOT NULL,
    ReleasedYear INT NOT NULL
);
GO

INSERT INTO Cars (CarID, CarName, Manufacturer, Price, ReleasedYear) VALUES
(1, 'Accord', 'Honda Motor Company', 24970.0000, 2021),
(3, 'Clarity', 'Honda Motor Company', 33400.0000, 2021),
(4, 'BMW 8 Series', 'BMW', 85000.0000, 2021),
(5, 'Audi A6', 'Audi', 14000.0000, 2020);
GO
```

### 3. Cấu hình database connection
Cập nhật thông tin trong `src/java/context/DBContext.java`:
```java
private static final String URL = "******************************************************************";
private static final String USER = "sa"; // Thay bằng username của bạn
private static final String PASSWORD = "123"; // Thay bằng password của bạn
```

### 4. Build và Deploy
1. Clean and Build project (Shift+F11)
2. Deploy to Tomcat server
3. Start Tomcat server

### 5. Test các URL
Sau khi deploy thành công, test các URL sau:

1. **Trang chính**: `http://localhost:8080/Automobile/`
2. **Test connection**: `http://localhost:8080/Automobile/test`
3. **Car management**: `http://localhost:8080/Automobile/cars`
4. **Test page**: `http://localhost:8080/Automobile/test.html`

### 6. Troubleshooting

#### Lỗi 404 - Servlet not found:
- Kiểm tra web.xml có đúng servlet mapping không
- Kiểm tra class CarController có trong package controller không
- Restart Tomcat server

#### Lỗi 500 - Database connection:
- Kiểm tra SQL Server đã chạy chưa
- Kiểm tra database MyStock đã được tạo chưa
- Kiểm tra username/password trong DBContext.java
- Kiểm tra JDBC driver đã được thêm vào lib chưa

#### Lỗi JSTL:
- Kiểm tra jstl-1.2.jar đã được thêm vào web/WEB-INF/lib/ chưa
- Clean and rebuild project
- Restart Tomcat

### 7. Cấu trúc project hoàn chỉnh:

```
Automobile/
├── src/
│   └── java/
│       ├── context/
│       │   └── DBContext.java
│       ├── dao/
│       │   ├── CarDAO.java
│       │   └── CarDAOImpl.java
│       ├── models/
│       │   └── Car.java
│       └── controller/
│           ├── CarController.java
│           └── TestController.java
├── web/
│   ├── WEB-INF/
│   │   ├── lib/
│   │   │   ├── jstl-1.2.jar
│   │   │   └── mssql-jdbc-12.8.0.jre17.jar
│   │   ├── views/
│   │   │   ├── list.jsp
│   │   │   ├── create.jsp
│   │   │   ├── edit.jsp
│   │   │   ├── details.jsp
│   │   │   ├── delete.jsp
│   │   │   └── error/
│   │   │       ├── 404.jsp
│   │   │       └── 500.jsp
│   │   └── web.xml
│   ├── index.html
│   └── test.html
├── database/
│   └── create_database.sql
├── README.md
└── DEPLOYMENT_GUIDE.md
```

### 8. Chức năng của ứng dụng:

- **Xem danh sách xe**: `/cars` hoặc `/cars?action=list`
- **Thêm xe mới**: `/cars?action=create`
- **Chỉnh sửa xe**: `/cars?action=edit&id={carID}`
- **Xem chi tiết**: `/cars?action=details&id={carID}`
- **Xóa xe**: `/cars?action=delete&id={carID}`

### 9. Validation rules:

- **Car ID**: Số nguyên dương, không được trùng
- **Car Name**: Bắt buộc, không được để trống
- **Manufacturer**: Bắt buộc, không được để trống
- **Price**: Số thực dương, lớn hơn 0
- **Released Year**: Số nguyên từ 1900 đến 2030
