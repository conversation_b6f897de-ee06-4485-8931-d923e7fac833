# Automobile Management System

Ứng dụng quản lý ô tô được xây dựng bằng Java Web (JSP, Servlet) với SQL Server database.

## Y<PERSON>u cầu hệ thống

- Java JDK 17 hoặc cao hơn
- Apache Tomcat 8.0 hoặc cao hơn
- SQL Server 2019 hoặc cao hơn
- NetBeans IDE (khuyến nghị)

## Cài đặt thư viện

### 1. SQL Server JDBC Driver
Tải xuống Microsoft JDBC Driver cho SQL Server từ:
https://docs.microsoft.com/en-us/sql/connect/jdbc/download-microsoft-jdbc-driver-for-sql-server

Sao chép file `mssql-jdbc-x.x.x.jre17.jar` vào thư mục `web/WEB-INF/lib/`

### 2. JSTL Library
Tải xuống JSTL 1.2 từ:
https://mvnrepository.com/artifact/javax.servlet/jstl/1.2

Sao chép file `jstl-1.2.jar` vào thư mục `web/WEB-INF/lib/`

## Cài đặt Database

1. Mở SQL Server Management Studio
2. Chạy script sau để tạo database và dữ liệu mẫu:

```sql
CREATE DATABASE MyStock;
GO
USE MyStock;
GO

CREATE TABLE Cars (
    CarID INT NOT NULL PRIMARY KEY,
    CarName VARCHAR(50) NOT NULL,
    Manufacturer VARCHAR(50) NOT NULL,
    Price MONEY NOT NULL,
    ReleasedYear INT NOT NULL
);
GO

INSERT INTO Cars (CarID, CarName, Manufacturer, Price, ReleasedYear) VALUES
(1, 'Accord', 'Honda Motor Company', 24970.0000, 2021),
(3, 'Clarity', 'Honda Motor Company', 33400.0000, 2021),
(4, 'BMW 8 Series', 'BMW', 85000.0000, 2021),
(5, 'Audi A6', 'Audi', 14000.0000, 2020);
GO
```

## Cấu hình Database Connection

Cập nhật thông tin kết nối database trong file `src/java/context/DBContext.java`:

```java
private static final String URL = "******************************************************************";
private static final String USER = "sa";
private static final String PASSWORD = "123";
```

Thay đổi `USER` và `PASSWORD` theo cấu hình SQL Server của bạn.

## Chạy ứng dụng

1. Mở project trong NetBeans IDE
2. Đảm bảo Tomcat server đã được cấu hình
3. Build project (F11)
4. Run project (F6)
5. Truy cập: http://localhost:8080/Automobile

## Chức năng

- **Xem danh sách xe**: Hiển thị tất cả xe trong database
- **Thêm xe mới**: Thêm xe mới với validation dữ liệu
- **Chỉnh sửa xe**: Cập nhật thông tin xe
- **Xem chi tiết**: Hiển thị thông tin chi tiết của xe
- **Xóa xe**: Xóa xe với xác nhận

## Cấu trúc Project

```
src/
├── java/
│   ├── context/
│   │   └── DBContext.java          # Database connection
│   ├── dao/
│   │   ├── CarDAO.java             # Data Access Object interface
│   │   └── CarDAOImpl.java         # DAO implementation
│   ├── models/
│   │   └── Car.java                # Car entity
│   └── controller/
│       └── CarController.java      # Servlet controller
web/
├── WEB-INF/
│   ├── views/
│   │   ├── list.jsp                # Car list page
│   │   ├── create.jsp              # Add new car page
│   │   ├── edit.jsp                # Edit car page
│   │   ├── details.jsp             # Car details page
│   │   └── delete.jsp              # Delete confirmation page
│   ├── lib/                        # JAR libraries
│   └── web.xml                     # Web configuration
└── index.html                      # Welcome page
```

## Validation Rules

- **Car ID**: Số nguyên dương, không được trùng
- **Car Name**: Bắt buộc, không được để trống
- **Manufacturer**: Bắt buộc, không được để trống
- **Price**: Số thực dương, lớn hơn 0
- **Released Year**: Số nguyên từ 1900 đến 2030

## Troubleshooting

### Lỗi kết nối database
- Kiểm tra SQL Server đã chạy
- Kiểm tra thông tin kết nối trong DBContext.java
- Đảm bảo database MyStock đã được tạo

### Lỗi JSTL
- Đảm bảo jstl-1.2.jar đã được thêm vào web/WEB-INF/lib/
- Restart Tomcat server

### Lỗi 404
- Kiểm tra URL mapping trong web.xml
- Đảm bảo project đã được deploy đúng cách
