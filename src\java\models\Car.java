package models;

import java.math.BigDecimal;

public class Car {
    private int carID;
    private String carName;
    private String manufacturer;
    private BigDecimal price;
    private int releasedYear;
    
    // Default constructor
    public Car() {
    }
    
    // Constructor with all parameters
    public Car(int carID, String carName, String manufacturer, BigDecimal price, int releasedYear) {
        this.carID = carID;
        this.carName = carName;
        this.manufacturer = manufacturer;
        this.price = price;
        this.releasedYear = releasedYear;
    }
    
    // Constructor without carID (for creating new cars)
    public Car(String carName, String manufacturer, BigDecimal price, int releasedYear) {
        this.carName = carName;
        this.manufacturer = manufacturer;
        this.price = price;
        this.releasedYear = releasedYear;
    }
    
    // Getters and Setters
    public int getCarID() {
        return carID;
    }
    
    public void setCarID(int carID) {
        this.carID = carID;
    }
    
    public String getCarName() {
        return carName;
    }
    
    public void setCarName(String carName) {
        this.carName = carName;
    }
    
    public String getManufacturer() {
        return manufacturer;
    }
    
    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public int getReleasedYear() {
        return releasedYear;
    }
    
    public void setReleasedYear(int releasedYear) {
        this.releasedYear = releasedYear;
    }
    
    @Override
    public String toString() {
        return "Car{" +
                "carID=" + carID +
                ", carName='" + carName + '\'' +
                ", manufacturer='" + manufacturer + '\'' +
                ", price=" + price +
                ", releasedYear=" + releasedYear +
                '}';
    }
}
