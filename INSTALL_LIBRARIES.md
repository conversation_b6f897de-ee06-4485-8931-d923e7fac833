# Hướng dẫn cài đặt thư viện

## Bước 1: <PERSON><PERSON><PERSON> xu<PERSON>ng thư viện JSTL

1. Truy cập: https://mvnrepository.com/artifact/javax.servlet/jstl/1.2
2. T<PERSON>i file `jstl-1.2.jar`
3. <PERSON>o chép vào thư mục `web/WEB-INF/lib/`

## Bước 2: <PERSON>ải xuống SQL Server JDBC Driver

1. Truy cập: https://docs.microsoft.com/en-us/sql/connect/jdbc/download-microsoft-jdbc-driver-for-sql-server
2. <PERSON><PERSON><PERSON> xuống Microsoft JDBC Driver 12.8 for SQL Server
3. Giải nén và tìm file `mssql-jdbc-12.8.0.jre17.jar` (hoặc phiên bản tương tự)
4. Sao chép vào thư mục `web/WEB-INF/lib/`

## Bước 3: Tạo database

1. Mở SQL Server Management Studio
2. Chạy script trong file `database/create_database.sql`

## Bước 4: <PERSON><PERSON>u hình kết nối database

Cậ<PERSON> nhật thông tin trong file `src/java/context/DBContext.java`:
- URL: ******************************************************************
- USER: sa (hoặc username của bạn)
- PASSWORD: 123 (hoặc password của bạn)

## Bước 5: Build và chạy project

1. Clean and Build project (Shift+F11)
2. Run project (F6)

## Cấu trúc thư mục web/WEB-INF/lib/ sau khi hoàn thành:

```
web/WEB-INF/lib/
├── jstl-1.2.jar
└── mssql-jdbc-12.8.0.jre17.jar
```

## Troubleshooting

### Nếu vẫn gặp lỗi 404:
1. Kiểm tra Tomcat server đã chạy
2. Kiểm tra project đã được deploy
3. Truy cập: http://localhost:8080/Automobile/cars

### Nếu gặp lỗi database:
1. Kiểm tra SQL Server đã chạy
2. Kiểm tra thông tin kết nối trong DBContext.java
3. Kiểm tra database MyStock đã được tạo

### Nếu gặp lỗi JSTL:
1. Kiểm tra jstl-1.2.jar đã được thêm vào lib
2. Restart Tomcat server
3. Clean and rebuild project
